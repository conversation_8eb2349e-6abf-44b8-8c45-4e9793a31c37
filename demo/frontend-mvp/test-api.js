#!/usr/bin/env node

// 简单的API测试脚本
import axios from 'axios';

const BASE_URL = 'http://localhost:8000/api/v1';

async function testAPI() {
  console.log('🚀 开始测试后端API...\n');

  try {
    // 跳过健康检查，直接测试核心功能
    console.log('⏭️  跳过健康检查，直接测试核心功能...\n');

    // 1. 测试创建对话
    console.log('1. 测试创建对话...');
    const conversationData = {
      title: '测试对话 - ' + new Date().toLocaleString(),
      status: 'active',
      learning_level: 'beginner'
    };
    
    const createResponse = await axios.post(`${BASE_URL}/conversations/`, conversationData);
    console.log('✅ 对话创建成功:', createResponse.data);
    const conversationId = createResponse.data.id;
    console.log('');

    // 2. 测试发送消息
    console.log('2. 测试发送消息...');
    const chatData = {
      message: '你好，我想学习React Hooks',
      context_limit: 5,
      use_search: true
    };
    
    const chatResponse = await axios.post(`${BASE_URL}/conversations/${conversationId}/chat`, chatData);
    console.log('✅ 消息发送成功:', {
      userMessage: chatResponse.data.message.content,
      assistantMessage: chatResponse.data.assistant_response.content.substring(0, 100) + '...',
      contextUsed: chatResponse.data.context_used?.length || 0,
      suggestions: chatResponse.data.suggestions?.length || 0
    });
    console.log('');

    // 3. 测试获取消息历史
    console.log('3. 测试获取消息历史...');
    const messagesResponse = await axios.get(`${BASE_URL}/conversations/${conversationId}/messages`);
    console.log('✅ 消息历史获取成功:', {
      messageCount: messagesResponse.data.length,
      messages: messagesResponse.data.map(msg => ({
        role: msg.role,
        content: msg.content.substring(0, 50) + '...'
      }))
    });
    console.log('');

    console.log('🎉 所有API测试通过！');
    console.log(`\n📝 测试对话ID: ${conversationId}`);
    console.log('💡 你可以在前端界面中使用这个对话ID来加载历史消息');

  } catch (error) {
    console.error('❌ API测试失败:', {
      message: error.message,
      status: error.response?.status,
      data: error.response?.data
    });
    
    if (error.code === 'ECONNREFUSED') {
      console.log('\n💡 提示: 请确保后端服务正在运行 (docker-compose up -d)');
    }
  }
}

// 如果直接运行此脚本
testAPI();
