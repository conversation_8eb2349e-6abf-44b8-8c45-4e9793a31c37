import {
  <PERSON>,
  <PERSON>lex,
  <PERSON><PERSON>,
  But<PERSON>,
  Container,
  HStack,
  Text,
} from '@chakra-ui/react'
import { useNavigate, useLocation } from 'react-router-dom'
import { FiHome } from 'react-icons/fi'

const Header = () => {
  const navigate = useNavigate()
  const location = useLocation()

  const isHomePage = location.pathname === '/'
  const isLearningPage = location.pathname.startsWith('/topic/')

  return (
    <Box
      bg="white"
      borderBottom="1px solid"
      borderColor="gray.200"
      position="sticky"
      top={0}
      zIndex={1000}
      boxShadow="sm"
    >
      <Container maxW="full" px={6}>
        <Flex h={16} alignItems="center" justifyContent="space-between">
          {/* Logo和标题 */}
          <HStack spacing={4}>
            <Heading
              size="lg"
              color="brand.500"
              cursor="pointer"
              onClick={() => navigate('/')}
              _hover={{ color: 'brand.600' }}
              transition="color 0.2s"
            >
              知深学习导师
            </Heading>
            <Text
              fontSize="sm"
              color="gray.500"
              fontWeight="medium"
              bg="brand.50"
              px={2}
              py={1}
              borderRadius="md"
            >
              MVP Demo
            </Text>
          </HStack>

          {/* 导航按钮 */}
          <HStack spacing={4}>
            {!isHomePage && (
              <Button
                leftIcon={<FiHome />}
                variant="ghost"
                size="sm"
                onClick={() => navigate('/')}
              >
                主题列表
              </Button>
            )}

            {isLearningPage && (
              <HStack spacing={2}>
                <Box
                  w={2}
                  h={2}
                  bg="green.400"
                  borderRadius="full"
                />
                <Text fontSize="sm" color="green.600" fontWeight="medium">
                  学习中
                </Text>
              </HStack>
            )}
          </HStack>
        </Flex>
      </Container>
    </Box>
  )
}

export default Header
