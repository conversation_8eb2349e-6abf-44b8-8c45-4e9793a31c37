import React, { useState, useRef, useEffect } from 'react'
import {
  Box,
  VStack,
  HStack,
  Text,
  Textarea,
  Button,
  Avatar,
  useColorModeValue,
  Flex,
  IconButton,
  Tooltip,
  Spinner,
  Alert,
  AlertIcon,
  AlertDescription,
  Container,
  Heading,
  Badge,
  Divider,
  useToast,
  Card,
  CardBody,
  CardHeader,
  Accordion,
  AccordionItem,
  AccordionButton,
  AccordionPanel,
  AccordionIcon,
  Code,
  Input,
  FormControl,
  FormLabel,
  Select,
} from '@chakra-ui/react'
import { FiSend, FiUser, FiCpu, FiMessageSquare, FiSettings, FiRefreshCw } from 'react-icons/fi'
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { Message } from '@/types'
import { chatService } from '@/services/chatService'

const ChatTestPage = () => {
  const [conversationId, setConversationId] = useState<string>('')
  const [inputValue, setInputValue] = useState('')
  const [isComposing, setIsComposing] = useState(false)
  const [messages, setMessages] = useState<Message[]>([])
  const [contextData, setContextData] = useState<string[]>([])
  const [suggestions, setSuggestions] = useState<string[]>([])
  const [debugInfo, setDebugInfo] = useState<any>({})
  
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const toast = useToast()
  const queryClient = useQueryClient()

  const bgColor = useColorModeValue('white', 'gray.800')
  const inputBgColor = useColorModeValue('gray.50', 'gray.700')
  const borderColor = useColorModeValue('gray.200', 'gray.600')

  // 创建对话
  const createConversationMutation = useMutation({
    mutationFn: (title: string) => chatService.createConversation(title),
    onSuccess: (conversation) => {
      setConversationId(conversation.id)
      setMessages([])
      setContextData([])
      setSuggestions([])
      toast({
        title: '对话创建成功',
        description: `对话ID: ${conversation.id}`,
        status: 'success',
        duration: 3000,
      })
    },
    onError: (error: any) => {
      toast({
        title: '创建对话失败',
        description: error.message || '未知错误',
        status: 'error',
        duration: 5000,
      })
    },
  })

  // 加载对话消息
  const loadMessagesMutation = useMutation({
    mutationFn: (convId: string) => chatService.getMessages(convId),
    onSuccess: (loadedMessages) => {
      setMessages(loadedMessages)
      toast({
        title: '消息加载成功',
        description: `加载了 ${loadedMessages.length} 条消息`,
        status: 'success',
        duration: 3000,
      })
    },
    onError: (error: any) => {
      toast({
        title: '加载消息失败',
        description: error.message || '未知错误',
        status: 'error',
        duration: 5000,
      })
    },
  })

  // 发送消息
  const sendMessageMutation = useMutation({
    mutationFn: (content: string) => chatService.sendMessage(conversationId, content),
    onSuccess: (response) => {
      // 添加用户消息和AI回复到消息列表
      setMessages(prev => [...prev, response.userMessage, response.assistantMessage])
      setContextData(response.contextUsed)
      setSuggestions(response.suggestions)
      setDebugInfo({
        lastRequest: {
          conversationId,
          message: inputValue,
          timestamp: new Date().toISOString()
        },
        lastResponse: response,
        apiEndpoint: `/conversations/${conversationId}/chat`
      })
      setInputValue('')
      scrollToBottom()
      
      toast({
        title: '消息发送成功',
        status: 'success',
        duration: 2000,
      })
    },
    onError: (error: any) => {
      toast({
        title: '发送消息失败',
        description: error.message || '未知错误',
        status: 'error',
        duration: 5000,
      })
    },
  })

  // 滚动到底部
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }

  // 自动滚动到底部（新消息时）
  useEffect(() => {
    if (messages.length > 0) {
      scrollToBottom()
    }
  }, [messages.length])

  const handleCreateConversation = () => {
    const title = `测试对话 - ${new Date().toLocaleString()}`
    createConversationMutation.mutate(title)
  }

  const handleLoadMessages = () => {
    if (!conversationId.trim()) {
      toast({
        title: '请先输入对话ID',
        status: 'warning',
        duration: 3000,
      })
      return
    }
    loadMessagesMutation.mutate(conversationId.trim())
  }

  const handleSendMessage = () => {
    if (!inputValue.trim() || sendMessageMutation.isPending) return
    if (!conversationId) {
      toast({
        title: '请先创建或加载对话',
        status: 'warning',
        duration: 3000,
      })
      return
    }
    
    sendMessageMutation.mutate(inputValue.trim())
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey && !isComposing) {
      e.preventDefault()
      handleSendMessage()
    }
  }

  return (
    <Container maxW="full" p={0}>
      <Flex h="100vh">
        {/* 左侧：对话界面 */}
        <Box flex={2} borderRight="1px solid" borderColor={borderColor}>
          <VStack h="full" spacing={0}>
            {/* 对话控制面板 */}
            <Box w="full" p={4} borderBottom="1px solid" borderColor={borderColor} bg={bgColor}>
              <VStack spacing={4}>
                <Heading size="md" color="brand.500">
                  <HStack>
                    <FiMessageSquare />
                    <Text>AI对话测试</Text>
                  </HStack>
                </Heading>
                
                <HStack w="full" spacing={3}>
                  <Button
                    leftIcon={<FiMessageSquare />}
                    colorScheme="brand"
                    onClick={handleCreateConversation}
                    isLoading={createConversationMutation.isPending}
                    size="sm"
                  >
                    创建新对话
                  </Button>
                  
                  <Input
                    placeholder="输入对话ID加载历史消息"
                    value={conversationId}
                    onChange={(e) => setConversationId(e.target.value)}
                    size="sm"
                    flex={1}
                  />
                  
                  <Button
                    leftIcon={<FiRefreshCw />}
                    onClick={handleLoadMessages}
                    isLoading={loadMessagesMutation.isPending}
                    size="sm"
                  >
                    加载消息
                  </Button>
                </HStack>
                
                {conversationId && (
                  <Badge colorScheme="green" fontSize="xs">
                    当前对话ID: {conversationId}
                  </Badge>
                )}
              </VStack>
            </Box>

            {/* 消息列表 */}
            <Box flex={1} overflowY="auto" w="full" p={4}>
              <VStack spacing={4} align="stretch">
                {messages.length === 0 ? (
                  <Box textAlign="center" py={12}>
                    <VStack spacing={4}>
                      <FiCpu size={48} color="gray.400" />
                      <Text color="gray.500" fontSize="lg">
                        创建对话开始测试！
                      </Text>
                      <Text color="gray.400" fontSize="sm">
                        这里将显示与真实后端API的对话
                      </Text>
                    </VStack>
                  </Box>
                ) : (
                  messages.map((message) => (
                    <MessageBubble key={message.id} message={message} />
                  ))
                )}
                
                {sendMessageMutation.isPending && (
                  <MessageBubble
                    message={{
                      id: 'pending',
                      role: 'assistant',
                      content: '',
                      timestamp: new Date().toISOString(),
                    }}
                    isLoading
                  />
                )}
                
                <div ref={messagesEndRef} />
              </VStack>
            </Box>

            {/* 输入区域 */}
            <Box
              w="full"
              p={4}
              borderTop="1px solid"
              borderColor={borderColor}
              bg={bgColor}
            >
              <HStack spacing={3}>
                <Textarea
                  value={inputValue}
                  onChange={(e) => setInputValue(e.target.value)}
                  onKeyPress={handleKeyPress}
                  onCompositionStart={() => setIsComposing(true)}
                  onCompositionEnd={() => setIsComposing(false)}
                  placeholder="输入你的问题..."
                  bg={inputBgColor}
                  border="1px solid"
                  borderColor={borderColor}
                  borderRadius="lg"
                  resize="none"
                  rows={3}
                  _focus={{
                    borderColor: 'brand.500',
                    boxShadow: '0 0 0 1px var(--chakra-colors-brand-500)',
                  }}
                />
                
                <Tooltip label="发送消息 (Enter)" placement="top" hasArrow>
                  <IconButton
                    aria-label="发送消息"
                    icon={<FiSend />}
                    colorScheme="brand"
                    size="lg"
                    isLoading={sendMessageMutation.isPending}
                    isDisabled={!inputValue.trim() || !conversationId}
                    onClick={handleSendMessage}
                  />
                </Tooltip>
              </HStack>
            </Box>
          </VStack>
        </Box>

        {/* 右侧：调试面板 */}
        <Box flex={1} bg="gray.50" overflowY="auto">
          <VStack spacing={4} p={4} align="stretch">
            <Heading size="md" color="purple.500">
              <HStack>
                <FiSettings />
                <Text>调试控制台</Text>
              </HStack>
            </Heading>

            {/* 上下文数据 */}
            <Card>
              <CardHeader pb={2}>
                <Heading size="sm">检索到的上下文</Heading>
              </CardHeader>
              <CardBody pt={0}>
                {contextData.length > 0 ? (
                  <VStack spacing={2} align="stretch">
                    {contextData.map((context, index) => (
                      <Box key={index} p={2} bg="blue.50" borderRadius="md" fontSize="sm">
                        <Text noOfLines={3}>{context}</Text>
                      </Box>
                    ))}
                  </VStack>
                ) : (
                  <Text fontSize="sm" color="gray.500">暂无上下文数据</Text>
                )}
              </CardBody>
            </Card>

            {/* 建议问题 */}
            <Card>
              <CardHeader pb={2}>
                <Heading size="sm">AI建议的问题</Heading>
              </CardHeader>
              <CardBody pt={0}>
                {suggestions.length > 0 ? (
                  <VStack spacing={2} align="stretch">
                    {suggestions.map((suggestion, index) => (
                      <Button
                        key={index}
                        size="sm"
                        variant="outline"
                        colorScheme="green"
                        onClick={() => setInputValue(suggestion)}
                        textAlign="left"
                        whiteSpace="normal"
                        height="auto"
                        py={2}
                      >
                        {suggestion}
                      </Button>
                    ))}
                  </VStack>
                ) : (
                  <Text fontSize="sm" color="gray.500">暂无建议问题</Text>
                )}
              </CardBody>
            </Card>

            {/* API调试信息 */}
            <Card>
              <CardHeader pb={2}>
                <Heading size="sm">API调试信息</Heading>
              </CardHeader>
              <CardBody pt={0}>
                {Object.keys(debugInfo).length > 0 ? (
                  <Code fontSize="xs" p={3} borderRadius="md" whiteSpace="pre-wrap">
                    {JSON.stringify(debugInfo, null, 2)}
                  </Code>
                ) : (
                  <Text fontSize="sm" color="gray.500">发送消息后显示调试信息</Text>
                )}
              </CardBody>
            </Card>
          </VStack>
        </Box>
      </Flex>
    </Container>
  )
}

// 消息气泡组件
interface MessageBubbleProps {
  message: Message
  isLoading?: boolean
}

const MessageBubble: React.FC<MessageBubbleProps> = ({ message, isLoading }) => {
  const isUser = message.role === 'user'
  const bgColor = useColorModeValue(
    isUser ? 'brand.500' : 'gray.100',
    isUser ? 'brand.600' : 'gray.700'
  )
  const textColor = useColorModeValue(
    isUser ? 'white' : 'gray.800',
    isUser ? 'white' : 'gray.100'
  )

  return (
    <HStack
      spacing={3}
      align="start"
      justify={isUser ? 'flex-end' : 'flex-start'}
    >
      {!isUser && (
        <Avatar size="sm" icon={<FiCpu />} bg="brand.500" color="white" />
      )}
      
      <Box
        bg={bgColor}
        color={textColor}
        px={4}
        py={3}
        borderRadius="lg"
        maxW="70%"
        position="relative"
      >
        {isLoading ? (
          <HStack spacing={2}>
            <Spinner size="sm" />
            <Text>AI正在思考...</Text>
          </HStack>
        ) : (
          <Text whiteSpace="pre-wrap" lineHeight="1.5">
            {message.content}
          </Text>
        )}
        
        {message.timestamp && (
          <Text fontSize="xs" opacity={0.7} mt={1}>
            {new Date(message.timestamp).toLocaleTimeString()}
          </Text>
        )}
      </Box>
      
      {isUser && (
        <Avatar size="sm" icon={<FiUser />} bg="gray.500" color="white" />
      )}
    </HStack>
  )
}

export default ChatTestPage
