import React, { useState } from 'react'
import {
  Box,
  VStack,
  HStack,
  Text,
  Button,
  Container,
  Heading,
  Alert,
  AlertIcon,
  AlertDescription,
  Code,
  useToast,
  Card,
  CardBody,
  CardHeader,
  Textarea,
  Input,
  FormControl,
  FormLabel,
} from '@chakra-ui/react'
import { FiPlay, FiRefreshCw } from 'react-icons/fi'

const ApiTestPage = () => {
  const [testResults, setTestResults] = useState<any[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [customUrl, setCustomUrl] = useState('/api/v1/utils/health-check/')
  const [customMethod, setCustomMethod] = useState('GET')
  const [customBody, setCustomBody] = useState('')
  const toast = useToast()

  const addResult = (test: string, success: boolean, data: any, error?: string) => {
    const result = {
      test,
      success,
      data,
      error,
      timestamp: new Date().toLocaleTimeString()
    }
    setTestResults(prev => [result, ...prev])
  }

  const testHealthCheck = async () => {
    try {
      const response = await fetch('/api/v1/utils/health-check/')
      const data = await response.json()
      addResult('健康检查', response.ok, data)
    } catch (error: any) {
      addResult('健康检查', false, null, error.message)
    }
  }

  const testCreateConversation = async () => {
    try {
      const response = await fetch('/api/v1/conversations/', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          title: '测试对话 - ' + new Date().toLocaleString(),
          status: 'active',
          learning_level: 'beginner'
        })
      })
      const data = await response.json()
      addResult('创建对话', response.ok, data)
    } catch (error: any) {
      addResult('创建对话', false, null, error.message)
    }
  }

  const testCustomRequest = async () => {
    try {
      const options: RequestInit = {
        method: customMethod,
        headers: {
          'Content-Type': 'application/json',
        }
      }

      if (customMethod !== 'GET' && customBody.trim()) {
        options.body = customBody
      }

      const response = await fetch(customUrl, options)
      const data = await response.json()
      addResult(`自定义请求 (${customMethod} ${customUrl})`, response.ok, data)
    } catch (error: any) {
      addResult(`自定义请求 (${customMethod} ${customUrl})`, false, null, error.message)
    }
  }

  const runAllTests = async () => {
    setIsLoading(true)
    setTestResults([])
    
    try {
      await testHealthCheck()
      await new Promise(resolve => setTimeout(resolve, 1000))
      await testCreateConversation()
    } catch (error) {
      console.error('测试过程中出错:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const clearResults = () => {
    setTestResults([])
  }

  return (
    <Container maxW="6xl" py={8}>
      <VStack spacing={8} align="stretch">
        <Box textAlign="center">
          <Heading size="lg" color="purple.500" mb={2}>
            API 连接测试工具
          </Heading>
          <Text color="gray.600">
            测试前端与后端API的连接状态
          </Text>
        </Box>

        {/* 快速测试按钮 */}
        <Card>
          <CardHeader>
            <Heading size="md">快速测试</Heading>
          </CardHeader>
          <CardBody>
            <HStack spacing={4} wrap="wrap">
              <Button
                leftIcon={<FiPlay />}
                colorScheme="blue"
                onClick={runAllTests}
                isLoading={isLoading}
                loadingText="测试中..."
              >
                运行所有测试
              </Button>
              
              <Button
                leftIcon={<FiPlay />}
                variant="outline"
                onClick={testHealthCheck}
                isDisabled={isLoading}
              >
                健康检查
              </Button>
              
              <Button
                leftIcon={<FiPlay />}
                variant="outline"
                onClick={testCreateConversation}
                isDisabled={isLoading}
              >
                创建对话
              </Button>
              
              <Button
                leftIcon={<FiRefreshCw />}
                variant="ghost"
                onClick={clearResults}
              >
                清空结果
              </Button>
            </HStack>
          </CardBody>
        </Card>

        {/* 自定义请求测试 */}
        <Card>
          <CardHeader>
            <Heading size="md">自定义API测试</Heading>
          </CardHeader>
          <CardBody>
            <VStack spacing={4} align="stretch">
              <HStack spacing={4}>
                <FormControl maxW="120px">
                  <FormLabel fontSize="sm">方法</FormLabel>
                  <select 
                    value={customMethod} 
                    onChange={(e) => setCustomMethod(e.target.value)}
                    style={{ padding: '8px', borderRadius: '6px', border: '1px solid #e2e8f0' }}
                  >
                    <option value="GET">GET</option>
                    <option value="POST">POST</option>
                    <option value="PUT">PUT</option>
                    <option value="DELETE">DELETE</option>
                  </select>
                </FormControl>
                
                <FormControl flex={1}>
                  <FormLabel fontSize="sm">URL</FormLabel>
                  <Input
                    value={customUrl}
                    onChange={(e) => setCustomUrl(e.target.value)}
                    placeholder="/api/v1/..."
                  />
                </FormControl>
              </HStack>
              
              {customMethod !== 'GET' && (
                <FormControl>
                  <FormLabel fontSize="sm">请求体 (JSON)</FormLabel>
                  <Textarea
                    value={customBody}
                    onChange={(e) => setCustomBody(e.target.value)}
                    placeholder='{"key": "value"}'
                    rows={3}
                  />
                </FormControl>
              )}
              
              <Button
                leftIcon={<FiPlay />}
                colorScheme="green"
                onClick={testCustomRequest}
                isDisabled={isLoading}
                alignSelf="flex-start"
              >
                发送请求
              </Button>
            </VStack>
          </CardBody>
        </Card>

        {/* 测试结果 */}
        <Card>
          <CardHeader>
            <Heading size="md">测试结果</Heading>
          </CardHeader>
          <CardBody>
            {testResults.length === 0 ? (
              <Text color="gray.500" textAlign="center" py={8}>
                暂无测试结果，点击上方按钮开始测试
              </Text>
            ) : (
              <VStack spacing={4} align="stretch">
                {testResults.map((result, index) => (
                  <Alert
                    key={index}
                    status={result.success ? 'success' : 'error'}
                    borderRadius="md"
                  >
                    <AlertIcon />
                    <Box flex={1}>
                      <AlertDescription>
                        <VStack align="stretch" spacing={2}>
                          <HStack justify="space-between">
                            <Text fontWeight="bold">{result.test}</Text>
                            <Text fontSize="sm" color="gray.500">
                              {result.timestamp}
                            </Text>
                          </HStack>
                          
                          {result.error && (
                            <Text color="red.600" fontSize="sm">
                              错误: {result.error}
                            </Text>
                          )}
                          
                          {result.data && (
                            <Code
                              p={2}
                              borderRadius="md"
                              fontSize="xs"
                              whiteSpace="pre-wrap"
                              maxH="200px"
                              overflowY="auto"
                            >
                              {JSON.stringify(result.data, null, 2)}
                            </Code>
                          )}
                        </VStack>
                      </AlertDescription>
                    </Box>
                  </Alert>
                ))}
              </VStack>
            )}
          </CardBody>
        </Card>
      </VStack>
    </Container>
  )
}

export default ApiTestPage
